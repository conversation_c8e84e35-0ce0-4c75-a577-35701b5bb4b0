package com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions;

import static com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants.OBFUSCATE;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.eq;

import com.intuit.appintgwkflw.wkflautomate.was.aop.constants.WASContextEnums;
import com.intuit.appintgwkflw.wkflautomate.was.aop.util.WASContextHandler;
import com.intuit.appintgwkflw.wkflautomate.was.app.providers.helper.ProviderHelper;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowError;
import com.intuit.appintgwkflw.wkflautomate.was.common.exception.WorkflowGeneralException;
import com.intuit.appintgwkflw.wkflautomate.was.common.featureflag.FeatureFlagManager;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.AccessVerifier;
import com.intuit.appintgwkflw.wkflautomate.was.common.util.WASContext;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.CustomWorkflowDecisionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.CreateDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DefinitionInstance;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DeleteDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.DisableDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.EnableDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.ReadCustomDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.handlers.definitions.UpdateDefinitionHandler;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.builders.MultiStepDefinitionActivityBuilder;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.multistep.dynamicbpmn.processor.DynamicBpmnDefinitionProcessor;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.definitions.MultiStepReminderTransformer;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.TemplateService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.UserContributionService;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.helper.LookupKeysMapper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.impl.AuthDetailsServiceHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.definition.services.validator.MultiStepValidator;
import com.intuit.appintgwkflw.wkflautomate.was.core.helper.TestHelper;
import com.intuit.appintgwkflw.wkflautomate.was.core.orchestrator.services.IdentityService;
import com.intuit.appintgwkflw.wkflautomate.was.core.processor.impl.BpmnProcessorImpl;
import com.intuit.appintgwkflw.wkflautomate.was.core.util.BpmnProcessorUtil;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.config.OverWatchConfig;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.DefinitionDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.entities.TemplateDetails;
import com.intuit.appintgwkflw.wkflautomate.was.dataaccess.repository.TemplateDetailsRepository;
import com.intuit.appintgwkflw.wkflautomate.was.entity.constants.WorkflowConstants;
import com.intuit.appintgwkflw.wkflautomate.was.entity.definition.DeployDefinitionResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.DefinitionType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.InternalStatus;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.ModelType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.RecordType;
import com.intuit.appintgwkflw.wkflautomate.was.entity.enums.Status;
import com.intuit.appintgwkflw.wkflautomate.was.entity.request.UcsVerifyAccessRequest;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.BpmnResponse;
import com.intuit.appintgwkflw.wkflautomate.was.entity.rest.definition.DmnResponse;
import com.intuit.v4.Authorization;
import com.intuit.v4.GlobalId;
import com.intuit.v4.Query;
import com.intuit.v4.RequestContext;
import com.intuit.v4.User;
import com.intuit.v4.common.Metadata;
import com.intuit.v4.common.MonthsOfYearEnum;
import com.intuit.v4.common.RecurTypeEnum;
import com.intuit.v4.common.WeekOfMonthEnum;
import com.intuit.v4.interaction.query.QueryHelper;
import com.intuit.v4.providers.ListResult;
import com.intuit.v4.workflows.Action;
import com.intuit.v4.workflows.Definition;
import com.intuit.v4.workflows.Template;
import com.intuit.v4.workflows.WorkflowStep;
import com.intuit.v4.workflows.WorkflowStep.ActionMapper;
import com.intuit.v4.workflows.definitions.InputParameter;
import com.intuit.appintgwkflw.wkflautomate.was.common.config.DefinitionRbacConfig;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.camunda.bpm.model.bpmn.Bpmn;
import org.camunda.bpm.model.bpmn.BpmnModelInstance;
import org.camunda.bpm.model.bpmn.instance.Process;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DefinitionServiceImplTest {

  @Spy @InjectMocks private DefinitionServiceImpl definitionService;

  @Mock private CreateDefinitionHandler createDefinitionHandler;

  @Mock private CreateDefinitionHandler customCreateDefinitionHandler;

  @Mock private CreateDefinitionHandler createMultiStepDefinitionHandler;

  @Mock private MultiStepDefinitionActivityBuilder multiStepDefinitionActivityBuilder;

  @Mock private TemplateService templateService;

  @Mock private ReadCustomDefinitionHandler readCustomDefinitionHandler;

  @Mock private TemplateDetails dmnTemplateDetail;
  @Mock private MultiStepReminderTransformer multiStepReminderTransformer;

  @Mock private TemplateDetails bpmnTemplateDetail;

  @Mock private DefinitionServiceHelper definitionServiceHelper;

  @Mock private DeleteDefinitionHandler deleteDefinitionHandler;

  @Mock private BpmnProcessorImpl bpmnProcessorImpl;

  @Mock private AuthDetailsServiceHelper authDetailsServiceHelper;

  @Mock private TemplateDetailsRepository templateDetailsRepository;

  @Mock private CustomWorkflowDecisionHandler dmnCreationHandler;

  private Definition definition = TestHelper.mockDefinitionEntity();

  @Rule public ExpectedException exceptionRule = ExpectedException.none();

  @Mock private DisableDefinitionHandler disableDefinitionHandler;

  @Mock private EnableDefinitionHandler enableDefinitionHandler;

  @Mock private UpdateDefinitionHandler updateDefinitionHandler;

  @Mock private ProviderHelper providerHelper;

  @Mock private IdentityService identityService;

  @Mock private FeatureFlagManager featureFlagManager;

  @Mock private SingleDefinitionRead singleDefinitionRead;

  @Mock private UserDefinitionRead userDefinitionRead;

  @Mock private LookupKeysMapper lookupKeysMapper;

  @Mock
  private OverWatchConfig overwatchConfig;

  @Mock
  private AccessVerifier accessVerifier;

  @Mock private WASContextHandler contextHandler;

  @Mock private DynamicBpmnDefinitionProcessor dynamicBpmnDefinitionProcessor;
  @Mock private MultiStepValidator multiStepValidator;
  @Mock private UserContributionService userContributionService;
  @Mock private DefinitionRbacConfig definitionRbacConfig;

  private static final String REALM_ID = "12345";

  private static final String BPMN_XML =
      TestHelper.readResourceAsString("bpmn/invoiceapproval.bpmn");
  private static final String DMN_XML =
      TestHelper.readResourceAsString("dmn/decision_invoiceapproval.dmn");

  private static final String BPMN_XML_STATEMENTS =
      TestHelper.readResourceAsString("bpmn/customScheduledActions.bpmn");
  private static final String BPMN_XML_STATEMENTS_ALL_RECURRENCE_RULES =
      TestHelper.readResourceAsString("bpmn/customScheduledActions2.bpmn");
  private static final String bpmnPathStmt = "schema/testData/xmlResponseBPMNStmt";
  private static final String DMN_XML_STATEMENTS =
      TestHelper.readResourceAsString("bpmn/decision_customScheduledActions.dmn");

  DefinitionInstance definitionInstance =
      TestHelper.mockDefinitionInstance(definition, bpmnTemplateDetail, null, null);

  Authorization authorization = TestHelper.mockAuthorization(REALM_ID);
  private static final String bpmnPath = "schema/testData/xmlResponseBPMN";

  private static final String LOCAL_ID_BPMN = "etet2-2434j2-3232fl-33ff";
  private static final String LOCAL_ID_DMN = "h342j3-n13m30-i12kjf-3k0p";
  private DefinitionDetails definitionDetailsBpmn =
      TestHelper.mockDefinitionDetailsWithId(bpmnTemplateDetail, authorization, LOCAL_ID_BPMN);
  private List<DefinitionDetails> definitionDetailsDmn =
          Collections.singletonList(
                  TestHelper.mockDefinitionDetailsWithParentId(
                          bpmnTemplateDetail, authorization, LOCAL_ID_DMN, LOCAL_ID_BPMN));
  private BpmnResponse bpmnResponse = TestHelper.mockBpmnResponse(definitionDetailsBpmn, bpmnPath);
  private BpmnResponse stmtBpmnResponse =
      TestHelper.mockBpmnResponse(definitionDetailsBpmn, bpmnPathStmt);
  private static final String dmnPath = "schema/testData/xmlResponseDMN";
  private List<DmnResponse> dmnResponseList =
          Collections.singletonList(TestHelper.mockDmnResponse(definitionDetailsDmn.get(0), dmnPath));

  @Before
  public void setup() {
    Mockito.when(
            multiStepDefinitionActivityBuilder.generateActivityInstanceMap(Mockito.any(), Mockito.anyList()))
            .thenReturn(new HashMap<>());
  }

  public void createDefinitionSetup() {
    Mockito.when(providerHelper.getTemplateDetails(Mockito.any()))
        .thenReturn(Optional.of(bpmnTemplateDetail));
    Mockito.when(templateService.getReferencedChildTemplates(eq(bpmnTemplateDetail)))
        .thenReturn(Optional.of(Collections.singletonList(dmnTemplateDetail)));
    Mockito.when(bpmnTemplateDetail.getTemplateData()).thenReturn(BPMN_XML.getBytes());
    Mockito.when(dmnTemplateDetail.getTemplateData()).thenReturn(DMN_XML.getBytes());

    Mockito.when(createDefinitionHandler.process(any(DefinitionInstance.class), eq(REALM_ID)))
        .thenReturn(definitionInstance);

    DeployDefinitionResponse deployDefinitionResponse = new DeployDefinitionResponse();
    Map<String, DeployDefinitionResponse.DeployedDefinition> deployedDefinitionMap =
        new HashMap<>();
    DeployDefinitionResponse.DeployedDefinition processDefinition =
        new DeployDefinitionResponse.DeployedDefinition();
    processDefinition.setId("id");
    processDefinition.setKey("key");
    deployedDefinitionMap.put("id", processDefinition);
    deployDefinitionResponse.setDeployedProcessDefinitions(deployedDefinitionMap);
    deployDefinitionResponse.setDeployedDecisionDefinitions(deployedDefinitionMap);
    authorization = new Authorization();
    authorization.putRealm(REALM_ID);
    authorization.putAuthId("453");
    WASContext.setAuthContext(authorization);
  }

  @Test
  public void testCreateDefinition() {
    createDefinitionSetup();
    Mockito.when(
            definitionServiceHelper.executeAsyncWorkflowTasks(
                any(DefinitionInstance.class), any(Authorization.class), eq(false)))
        .thenReturn(definition);
    Mockito.when(bpmnTemplateDetail.getTemplateName()).thenReturn("invoiceapproval");
    Mockito.doNothing().when(contextHandler).addKey(Mockito.any(WASContextEnums.class), Mockito.anyString());

    // RBAC not enabled, should proceed
    Mockito.when(definitionRbacConfig.isEnabledForOperation("CREATE")).thenReturn(false);
    Definition createdDefinition = definitionService.createDefinition(definition, authorization);
    Assert.assertNotNull(createdDefinition);
    Mockito.verify(contextHandler, Mockito.times(1)).addKey(Mockito.any(WASContextEnums.class), Mockito.anyString());
  }

  @Test
  public void testCreateDefinition_RbacEnabled_AccessDenied() {
    createDefinitionSetup();
    // Set up template name to ensure getWorkflowTypeFromDefinition returns a valid workflow type
    // Using "customApproval" which maps to "approval" action key in CustomWorkflowType enum
    Template template = new Template();
    template.setName("customApproval");
    definition.setTemplate(template);

    Mockito.when(definitionRbacConfig.isEnabledForOperation("CREATE")).thenReturn(true);
    Mockito.when(accessVerifier.verifyUserAccess("approval", "CREATE")).thenReturn(false);
    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS.getErrorMessage());
    definitionService.createDefinition(definition, authorization);
  }

  @Test
  public void testCreateDefinition_RbacEnabled_AccessGranted() {
    createDefinitionSetup();
    // Using "invoiceapproval" which maps to "approval" action key but is not a custom template
    Template template = new Template();
    template.setName("invoiceapproval");
    definition.setTemplate(template);

    // Mock all required dependencies for successful creation
    Mockito.when(definitionRbacConfig.isEnabledForOperation("CREATE")).thenReturn(true);
    Mockito.when(accessVerifier.verifyUserAccess("approval", "CREATE")).thenReturn(true);

    Mockito.when(definitionServiceHelper.executeAsyncWorkflowTasks(
        Mockito.any(), Mockito.any(), Mockito.anyBoolean()))
        .thenReturn(definition);
    Mockito.when(bpmnTemplateDetail.getTemplateName()).thenReturn("invoiceapproval");
    Mockito.doNothing().when(contextHandler).addKey(Mockito.any(WASContextEnums.class), Mockito.anyString());

    Definition result = definitionService.createDefinition(definition, authorization);
    Assert.assertNotNull(result);
  }

  @Test
  public void testCreateDefinition_TemplateNotFound() {
    Mockito.doThrow(new WorkflowGeneralException(WorkflowError.TEMPLATE_NOT_FOUND))
        .when(providerHelper)
        .validateRequest(definition, authorization);
    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.TEMPLATE_NOT_FOUND.getErrorMessage());
    definitionService.createDefinition(definition, authorization);
  }

  @Test
  public void testCreateDefinition_TemplateDoesNotExist() {
    definition.setTemplate(new Template().id(TestHelper.getGlobalId("abc")));
    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.TEMPLATE_DOES_NOT_EXIST.getErrorMessage());
    definitionService.createDefinition(definition, authorization);
  }

  @Test
  public void parseBpmnUsingModelStream() {
    String xml = TestHelper.readResourceAsString(bpmnPath);
    BpmnModelInstance modelInstance =
        Bpmn.readModelFromStream(new ByteArrayInputStream(xml.getBytes(StandardCharsets.UTF_8)));
    Assert.assertNotNull(modelInstance);
    Collection<Process> processes = modelInstance.getModelElementsByType(Process.class);
    Assert.assertNotNull(processes);
    Assert.assertEquals(1, processes.size());
  }

  @Test
  public void testNullBpmnError() {
    String xml = null;
    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.EMPTY_BPMN_EXCEPTION.getErrorMessage());
    BpmnProcessorUtil.getBpmnModelInstanceFromXml(xml);
  }

  @Test
  public void fetch_NullAuthorization_TemplateDoesNotExist() {
    definition.setTemplate(new Template().id(TestHelper.getGlobalId("abc")));
    exceptionRule.expect(NullPointerException.class);
    definitionService.getDefinitionList(null, null);
  }

  @Test
  public void testGetDefinition() {
    Authorization authorization = new Authorization();
    authorization.putRealm(REALM_ID);
    authorization.putAuthId("453");
    Optional<List<DefinitionDetails>> defintiionDetails =
        definitionServiceHelper.getDefinitionList(Long.parseLong(REALM_ID), null);

    Assert.assertNotNull(defintiionDetails);
  }

  @Test
  public void testGetDefinitionWithoutWorkflowSteps() {
    Authorization authorization = new Authorization();
    authorization.putRealm(REALM_ID);
    authorization.putAuthId("453");
    WASContext.setAuthContext(authorization);
    DefinitionDetails mockDefinitionDetails = new DefinitionDetails("11", "11", "11", ModelType.BPMN,
        Status.ENABLED, InternalStatus.DELETED, 1L,
        1L, "wkflw", "engagement", "mock",
        RecordType.ENGAGEMENT, 1,"{}", 0,  "test-id",
        DefinitionType.SINGLE, "{}", "depId", "customNotification", 1,new Date(), new Date(), 4231L, new Date(), 4231L, 123241234L);
    mockDefinitionDetails.setModifiedByUserId(1234567890L);
    mockDefinitionDetails.setCreatedByUserId(98765L);
    mockDefinitionDetails.setModifiedDate(new Timestamp(new Date().getTime()));
    mockDefinitionDetails.setCreatedDate(new Timestamp(new Date().getTime()));
    Mockito.when(definitionServiceHelper.getDefinitionListWithoutWorkflowSteps(Long.parseLong(REALM_ID), null))
        .thenReturn(Optional.of(Collections.singletonList(mockDefinitionDetails)));
    Mockito.when(definitionServiceHelper.getMetadata(authorization, mockDefinitionDetails))
        .thenCallRealMethod();
    List<Definition> definitionDetails =
        definitionService.getDefinitionList(authorization, null);

    Assert.assertNotNull(definitionDetails);
    Assert.assertEquals("1234567890", definitionDetails.get(0).getMeta().getUpdatedBy().getId().getLocalId());
    Assert.assertEquals("98765", definitionDetails.get(0).getMeta().getCreatedBy().getId().getLocalId());
    Assert.assertNotNull(definitionDetails.get(0).getMeta().getUpdated());
    Assert.assertNotNull(definitionDetails.get(0).getMeta().getCreated());
    Assert.assertEquals(definitionDetails.get(0).getWorkflowSteps().size(),0);
    Assert.assertEquals(definitionDetails.get(0).getTemplate().getVersion(), "1");
  }

  @Test
  public void testDeleteDefinition_RbacDisabled() {
    definition.setId(GlobalId.create(REALM_ID, "def-id"));
    Authorization authorization = new Authorization();
    authorization.putRealm(REALM_ID);
    authorization.putAuthId("453");
    DefinitionInstance def = new DefinitionInstance(definition, null, null, null);

    // Mock definitionServiceHelper.getWorkflowType for the new implementation
    Mockito.when(definitionServiceHelper.getWorkflowType("def-id", REALM_ID)).thenReturn("approval");
    Mockito.when(deleteDefinitionHandler.process(Mockito.any(), Mockito.any()))
        .thenReturn(def.getDefinition());

    // RBAC not enabled, should proceed
    Mockito.when(definitionRbacConfig.isEnabledForOperation("DELETE")).thenReturn(false);
    Definition definitionDetails = definitionService.deleteDefinition(definition, authorization);
    Assert.assertNotNull(definitionDetails);
  }

  @Test
  public void testDeleteDefinition_RbacEnabled_AccessDenied() {
    definition.setId(GlobalId.create(REALM_ID, "def-id"));
    Authorization testAuthorization = new Authorization();
    testAuthorization.putRealm(REALM_ID);
    testAuthorization.putAuthId("453");

    // Mock definitionServiceHelper.getWorkflowType to return "approval"
    Mockito.when(definitionServiceHelper.getWorkflowType("def-id", REALM_ID)).thenReturn("approval");
    Mockito.when(definitionRbacConfig.isEnabledForOperation("DELETE")).thenReturn(true);
    Mockito.when(accessVerifier.verifyUserAccess("approval", "DELETE")).thenReturn(false);

    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS.getErrorMessage());
    definitionService.deleteDefinition(definition, testAuthorization);
  }

  @Test
  public void testDeleteDefinition_RbacEnabled_AccessGranted() {
    definition.setId(GlobalId.create(REALM_ID, "def-id"));
    Authorization testAuthorization = new Authorization();
    testAuthorization.putRealm(REALM_ID);
    testAuthorization.putAuthId("453");
    DefinitionInstance testDefinitionInstance = new DefinitionInstance(definition, null, null, null);

    // Mock definitionServiceHelper.getWorkflowType to return "approval"
    Mockito.when(definitionServiceHelper.getWorkflowType("def-id", REALM_ID)).thenReturn("approval");
    Mockito.when(definitionRbacConfig.isEnabledForOperation("DELETE")).thenReturn(true);
    Mockito.when(accessVerifier.verifyUserAccess("approval", "DELETE")).thenReturn(true);
    Mockito.when(deleteDefinitionHandler.process(Mockito.any(), Mockito.any()))
        .thenReturn(testDefinitionInstance.getDefinition());

    Definition definitionDetails = definitionService.deleteDefinition(definition, testAuthorization);
    Assert.assertNotNull(definitionDetails);
  }

  @Test
  public void testDisableDefinition() {
    definition.setId(GlobalId.create(REALM_ID, "def-id"));
    Authorization authorization = new Authorization();
    authorization.putRealm(REALM_ID);
    authorization.putAuthId("453");

    // Mock the workflow type and RBAC config
    Mockito.when(definitionServiceHelper.getWorkflowType("def-id", REALM_ID)).thenReturn("default");
    Mockito.when(definitionRbacConfig.isEnabledForOperation("DISABLED")).thenReturn(false);

    DefinitionInstance def = new DefinitionInstance(definition, null, null, null);
    Mockito.when(disableDefinitionHandler.process(Mockito.any(), Mockito.any())).thenReturn(def);
    Definition definitionDetails = definitionService.disableDefinition(definition, authorization);
    Assert.assertNotNull(definitionDetails);
  }

  @Test
  public void testDisableDefinition_RbacDisabled() {
    definition.setId(GlobalId.create(REALM_ID, "def-id"));
    Authorization authorization = new Authorization();
    authorization.putRealm(REALM_ID);
    authorization.putAuthId("453");
    DefinitionInstance def = new DefinitionInstance(definition, null, null, null);

    // Mock definitionServiceHelper.getWorkflowType to return "approval"
    Mockito.when(definitionServiceHelper.getWorkflowType("def-id", REALM_ID)).thenReturn("approval");
    // RBAC disabled for DISABLE operation
    Mockito.when(definitionRbacConfig.isEnabledForOperation("DISABLED")).thenReturn(false);
    Mockito.when(disableDefinitionHandler.process(Mockito.any(), Mockito.any())).thenReturn(def);

    Definition definitionDetails = definitionService.disableDefinition(definition, authorization);
    Assert.assertNotNull(definitionDetails);
  }

  @Test
  public void testDisableDefinition_RbacEnabled_AccessDenied() {
    definition.setId(GlobalId.create(REALM_ID, "def-id"));
    Authorization testAuthorization = new Authorization();
    testAuthorization.putRealm(REALM_ID);
    testAuthorization.putAuthId("453");

    // Mock definitionServiceHelper.getWorkflowType to return "approval"
    Mockito.when(definitionServiceHelper.getWorkflowType("def-id", REALM_ID)).thenReturn("approval");
    Mockito.when(definitionRbacConfig.isEnabledForOperation("DISABLED")).thenReturn(true);
    Mockito.when(accessVerifier.verifyUserAccess("approval", "DISABLED")).thenReturn(false);

    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS.getErrorMessage());
    definitionService.disableDefinition(definition, testAuthorization);
  }

  @Test
  public void testDisableDefinition_RbacEnabled_AccessGranted() {
    definition.setId(GlobalId.create(REALM_ID, "def-id"));
    Authorization testAuthorization = new Authorization();
    testAuthorization.putRealm(REALM_ID);
    testAuthorization.putAuthId("453");
    DefinitionInstance testDefinitionInstance = new DefinitionInstance(definition, null, null, null);

    // Mock definitionServiceHelper.getWorkflowType to return "approval"
    Mockito.when(definitionServiceHelper.getWorkflowType("def-id", REALM_ID)).thenReturn("approval");
    Mockito.when(definitionRbacConfig.isEnabledForOperation("DISABLED")).thenReturn(true);
    Mockito.when(accessVerifier.verifyUserAccess("approval", "DISABLED")).thenReturn(true);
    Mockito.when(disableDefinitionHandler.process(Mockito.any(), Mockito.any()))
        .thenReturn(testDefinitionInstance);

    Definition definitionDetails = definitionService.disableDefinition(definition, testAuthorization);
    Assert.assertNotNull(definitionDetails);
  }

  @Test
  public void testEnableDefinition() {
    definition.setId(GlobalId.create(REALM_ID, "def-id"));
    Authorization authorization = new Authorization();
    authorization.putRealm(REALM_ID);
    authorization.putAuthId("453");

    // Mock the workflow type and RBAC config
    Mockito.when(definitionServiceHelper.getWorkflowType("def-id", REALM_ID)).thenReturn("default");
    Mockito.when(definitionRbacConfig.isEnabledForOperation("ENABLED")).thenReturn(false);

    DefinitionInstance def = new DefinitionInstance(definition, null, null, null);
    Mockito.when(enableDefinitionHandler.process(Mockito.any(), Mockito.any())).thenReturn(def);
    Definition definitionDetails = definitionService.enableDefinition(definition, authorization);
    Assert.assertNotNull(definitionDetails);
  }

  @Test
  public void testEnableDefinition_RbacDisabled() {
    definition.setId(GlobalId.create(REALM_ID, "def-id"));
    Authorization authorization = new Authorization();
    authorization.putRealm(REALM_ID);
    authorization.putAuthId("453");
    DefinitionInstance def = new DefinitionInstance(definition, null, null, null);

    // Mock definitionServiceHelper.getWorkflowType to return "approval"
    Mockito.when(definitionServiceHelper.getWorkflowType("def-id", REALM_ID)).thenReturn("approval");
    // RBAC disabled for ENABLE operation
    Mockito.when(definitionRbacConfig.isEnabledForOperation("ENABLED")).thenReturn(false);
    Mockito.when(enableDefinitionHandler.process(Mockito.any(), Mockito.any())).thenReturn(def);

    Definition definitionDetails = definitionService.enableDefinition(definition, authorization);
    Assert.assertNotNull(definitionDetails);
  }

  @Test
  public void testEnableDefinition_RbacEnabled_AccessDenied() {
    definition.setId(GlobalId.create(REALM_ID, "def-id"));
    Authorization testAuthorization = new Authorization();
    testAuthorization.putRealm(REALM_ID);
    testAuthorization.putAuthId("453");

    // Mock definitionServiceHelper.getWorkflowType to return "approval"
    Mockito.when(definitionServiceHelper.getWorkflowType("def-id", REALM_ID)).thenReturn("approval");
    Mockito.when(definitionRbacConfig.isEnabledForOperation("ENABLED")).thenReturn(true);
    Mockito.when(accessVerifier.verifyUserAccess("approval", "ENABLED")).thenReturn(false);

    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS.getErrorMessage());
    definitionService.enableDefinition(definition, testAuthorization);
  }

  @Test
  public void testEnableDefinition_RbacEnabled_AccessGranted() {
    definition.setId(GlobalId.create(REALM_ID, "def-id"));
    Authorization testAuthorization = new Authorization();
    testAuthorization.putRealm(REALM_ID);
    testAuthorization.putAuthId("453");
    DefinitionInstance testDefinitionInstance = new DefinitionInstance(definition, null, null, null);

    // Mock definitionServiceHelper.getWorkflowType to return "approval"
    Mockito.when(definitionServiceHelper.getWorkflowType("def-id", REALM_ID)).thenReturn("approval");
    Mockito.when(definitionRbacConfig.isEnabledForOperation("ENABLED")).thenReturn(true);
    Mockito.when(accessVerifier.verifyUserAccess("approval", "ENABLED")).thenReturn(true);
    Mockito.when(enableDefinitionHandler.process(Mockito.any(), Mockito.any()))
        .thenReturn(testDefinitionInstance);

    Definition definitionDetails = definitionService.enableDefinition(definition, testAuthorization);
    Assert.assertNotNull(definitionDetails);
  }

  @SuppressWarnings("unchecked")
  @Test
  public void testGetDefinitionReadOneUserDefinition() throws IOException {
    DefinitionDetails definitionDetailsDmn = new DefinitionDetails();
    definitionDetailsDmn.setDefinitionId("dmnId");
    definitionDetailsDmn.setDefinitionName("invoice");
    definitionDetailsDmn.setOwnerId(12345L);
    definitionDetailsDmn.setModelType(ModelType.DMN);

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    DefinitionDetails definitionDetails = buildDefinitionDetails(
        BPMN_XML_STATEMENTS_ALL_RECURRENCE_RULES.getBytes(),12345L);
    definitionDetailsList.add(definitionDetails);
    definitionDetailsList.add(definitionDetailsDmn);
    WASContext.setAuthContext(authorization);
    Mockito.when(definitionServiceHelper.getMetadata(authorization, definitionDetails))
        .thenCallRealMethod();
    Mockito.when(definitionServiceHelper.findByDefinitionIdOrParentId("defId"))
        .thenReturn(definitionDetailsList);
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("12345");

    Mockito.when(userDefinitionRead.getBPMNXMLDefinition(any(DefinitionDetails.class)))
        .thenReturn(bpmnResponse);

    Mockito.when(definitionServiceHelper.getDMNXMLDefinition(Mockito.any()))
        .thenReturn(dmnResponseList);

    Template template = new Template();
    Mockito.when(bpmnProcessorImpl.processBpmn(
                    any(DefinitionInstance.class), any(GlobalId.class), anyBoolean()))
            .thenReturn(template);
    Definition definition =
        definitionService.getDefinitionReadOne("defId", TestHelper.getGlobalId("abc"), false);
    Assert.assertNotNull(definition);
    Assert.assertNotNull(definition.getMeta());
    Assert.assertNotNull(definition.getDescription());
    Assert.assertNotNull(definition.getDisplayName());
  }

  @Test
  public void testGetDefinitionReadOneSingleDefinition() throws IOException {
    WASContext.setAuthContext(authorization);
    DefinitionDetails definitionDetails = buildDefinitionDetails(BPMN_XML.getBytes());
    definitionDetails.setPlaceholderValue("abc");
    definitionDetails.setOwnerId(Long.valueOf(REALM_ID));
    DefinitionDetails definitionDetailsDmn = new DefinitionDetails();
    definitionDetailsDmn.setDefinitionId("dmnId");
    definitionDetailsDmn.setDefinitionName("invoice");
    definitionDetailsDmn.setModelType(ModelType.DMN);

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();

    definitionDetailsList.add(definitionDetails);
    definitionDetailsList.add(definitionDetailsDmn);
    Mockito.when(definitionServiceHelper.findByDefinitionIdOrParentId("defId"))
        .thenReturn(definitionDetailsList);
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("12345");
    Mockito.when(definitionServiceHelper.getMetadata(authorization, definitionDetails))
        .thenReturn(new Metadata().createdBy(new User().id(TestHelper.getGlobalId("12"))));

    Mockito.when(singleDefinitionRead.getBPMNXMLDefinition(any(DefinitionDetails.class)))
        .thenReturn(bpmnResponse);

    Mockito.when(definitionServiceHelper.getDMNXMLDefinition(Mockito.any()))
        .thenReturn(dmnResponseList);

    Template template = new Template();
    template.setName("customApproval");
    Mockito.when(bpmnProcessorImpl.processBpmn(
                    any(DefinitionInstance.class), any(GlobalId.class), anyBoolean()))
            .thenReturn(template);
    Definition definition =
        definitionService.getDefinitionReadOne("defId", TestHelper.getGlobalId("abc"), false);
    Assert.assertNotNull(definition);
    Assert.assertNotNull(definition.getMeta());
    Assert.assertNotNull(definition.getDescription());
    Assert.assertNotNull(definition.getDisplayName());

    Mockito.when(definitionServiceHelper.isActivityDetailsPresent(any())).thenReturn(true);
    Definition multiStepDef =
            definitionService.getDefinitionReadOne("defId", TestHelper.getGlobalId("abc"), true);
    Assert.assertNotNull(multiStepDef);
    Assert.assertNotNull(multiStepDef.getStatus());
    Assert.assertNotNull(multiStepDef.getRecordType());
  }

  @Test
  public void testGetDefinitionReadOneSingleDefinitionObfuscateException() throws IOException {
    final QueryHelper queryHelper = mockQueryHelperWithArgsWithoutObfuscateFlag("defId");
    RequestContext context = Mockito.mock(RequestContext.class);
    DefinitionDetails definitionDetails = buildDefinitionDetails(BPMN_XML.getBytes());
    definitionDetails.setPlaceholderValue("abc");
    DefinitionDetails definitionDetailsDmn = new DefinitionDetails();
    definitionDetailsDmn.setDefinitionId("dmnId");
    definitionDetailsDmn.setDefinitionName("invoice");
    definitionDetailsDmn.setModelType(ModelType.DMN);
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(definitionDetails);
    definitionDetailsList.add(definitionDetailsDmn);
    try {
      definitionService.getDefinitionWithObfuscatedValues(context, queryHelper, false);
    } catch (Exception e) {
      Assert.assertEquals(e.getMessage(), WorkflowError.INVALID_INPUT.getErrorMessage());
    }
  }

  @SuppressWarnings("unchecked")
  @Test
  public void testGetDefinitionReadOneIoException() throws IOException {
    DefinitionDetails definitionDetailsDmn = new DefinitionDetails();
    definitionDetailsDmn.setDefinitionId("dmnId");
    definitionDetailsDmn.setDefinitionName("invoice");
    definitionDetailsDmn.setModelType(ModelType.DMN);
    definitionDetailsDmn.setOwnerId(12345L);
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();

    definitionDetailsList.add(buildDefinitionDetails(BPMN_XML_STATEMENTS_ALL_RECURRENCE_RULES.getBytes(),12345L));
    definitionDetailsList.add(definitionDetailsDmn);
    Mockito.when(definitionServiceHelper.findByDefinitionIdOrParentId("defId"))
        .thenReturn(definitionDetailsList);
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("12345");

    Mockito.when(userDefinitionRead.getBPMNXMLDefinition(any(DefinitionDetails.class)))
        .thenReturn(bpmnResponse);

    Mockito.when(definitionServiceHelper.getDMNXMLDefinition(Mockito.any()))
        .thenReturn(dmnResponseList);

    Mockito.when(bpmnProcessorImpl.processBpmn(
                    any(DefinitionInstance.class), any(GlobalId.class), anyBoolean()))
            .thenReturn(new IOException());
    Definition definition =
        definitionService.getDefinitionReadOne("defId", TestHelper.getGlobalId("abc"), false);
    Assert.assertNotNull(definition);
    Assert.assertNull(definition.getMeta());
    Assert.assertNull(definition.getDescription());
    Assert.assertNull(definition.getDisplayName());

    Definition multiStepDef =
            definitionService.getDefinitionReadOne("defId", TestHelper.getGlobalId("abc"), true);
    Assert.assertNotNull(multiStepDef);
    Assert.assertNotNull(multiStepDef.getStatus());
    Assert.assertNotNull(multiStepDef.getRecordType());
  }

  @SuppressWarnings("unchecked")
  @Test
  public void testGetDefinitionListWithWorkflowSteps() throws IOException {
    DefinitionDetails definitionDetails = buildDefinitionDetails(
        BPMN_XML_STATEMENTS_ALL_RECURRENCE_RULES.getBytes(),12345L);
    DefinitionDetails definitionDetailsDmn = new DefinitionDetails();
    definitionDetailsDmn.setDefinitionId("dmnId");
    definitionDetailsDmn.setDefinitionName("invoice");
    definitionDetailsDmn.setModelType(ModelType.DMN);
    definitionDetailsDmn.setOwnerId(12345L);
    WASContext.setAuthContext(authorization);
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();

    definitionDetailsList.add(definitionDetails);
    definitionDetailsList.add(definitionDetailsDmn);
    Mockito.when(definitionServiceHelper.findByDefinitionIdOrParentId("defId"))
        .thenReturn(definitionDetailsList);
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("12345");

    Mockito.when(userDefinitionRead.getBPMNXMLDefinition(any(DefinitionDetails.class)))
        .thenReturn(bpmnResponse);

    Mockito.when(definitionServiceHelper.getDMNXMLDefinition(Mockito.any()))
        .thenReturn(dmnResponseList);

    Mockito.when(bpmnProcessorImpl.processBpmn(
            any(DefinitionInstance.class), any(GlobalId.class), anyBoolean()))
        .thenReturn(new IOException());

    List<DefinitionDetails> definitionDetailList = new ArrayList<>();
    definitionDetailList.add(definitionDetails);

    QueryHelper queryHelper = Mockito.mock(QueryHelper.class);
    Mockito.when(
            definitionServiceHelper.getDefinitionList(
                Long.parseLong(authorization.getRealm()), queryHelper))
        .thenReturn(Optional.ofNullable(definitionDetailList));
    Mockito.when(definitionServiceHelper.getMetadata(authorization, definitionDetails))
        .thenCallRealMethod();

    List<Definition> definitionsList =
        definitionService.getDefinitionListWithWorkflowSteps(authorization, queryHelper);
    Assert.assertNotNull(definitionsList);
    Assert.assertNotNull(definitionsList.stream().findFirst().get().getMeta());
    Assert.assertNotNull(definitionsList.stream().findFirst().get().getDescription());
    Assert.assertNotNull(definitionsList.stream().findFirst().get().getDisplayName());
  }

  @Test
  public void testGetDefinitionListWithWorkflowStepsRecordTypeNull() throws IOException {
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId("defId");
    definitionDetails.setDefinitionName("invoice");
    definitionDetails.setModelType(ModelType.BPMN);
    definitionDetails.setRecordType(null);
    definitionDetails.setStatus(Status.ENABLED);
    definitionDetails.setTemplateDetails(new TemplateDetails());
    definitionDetails.setDescription("Description");
    definitionDetails.setDefinitionName("displayName");
    definitionDetails.setOwnerId(12345L);

    DefinitionDetails definitionDetailsDmn = new DefinitionDetails();
    definitionDetailsDmn.setDefinitionId("dmnId");
    definitionDetailsDmn.setDefinitionName("invoice");
    definitionDetailsDmn.setModelType(ModelType.DMN);

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();

    definitionDetailsList.add(definitionDetails);
    definitionDetailsList.add(definitionDetailsDmn);
    Mockito.when(definitionServiceHelper.getMetadata(authorization, definitionDetails))
        .thenCallRealMethod();
    Mockito.when(definitionServiceHelper.findByDefinitionIdOrParentId("defId"))
        .thenReturn(definitionDetailsList);
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("12345");

    Mockito.when(userDefinitionRead.getBPMNXMLDefinition(any(DefinitionDetails.class)))
        .thenReturn(bpmnResponse);

    Mockito.when(definitionServiceHelper.getDMNXMLDefinition(Mockito.any()))
        .thenReturn(dmnResponseList);

    Mockito.when(bpmnProcessorImpl.processBpmn(
                    any(DefinitionInstance.class), any(GlobalId.class), anyBoolean()))
            .thenReturn(new IOException());

    List<DefinitionDetails> definitionDetailList = new ArrayList<>();
    definitionDetailList.add(definitionDetails);

    QueryHelper queryHelper = Mockito.mock(QueryHelper.class);
    Mockito.when(
            definitionServiceHelper.getDefinitionList(
                Long.parseLong(authorization.getRealm()), queryHelper))
        .thenReturn(Optional.ofNullable(definitionDetailList));

    List<Definition> definitionsList =
        definitionService.getDefinitionListWithWorkflowSteps(authorization, queryHelper);
    Assert.assertNotNull(definitionsList);
    Assert.assertNotNull(definitionsList.stream().findFirst().get().getMeta());
    Assert.assertNotNull(definitionsList.stream().findFirst().get().getDescription());
    Assert.assertNotNull(definitionsList.stream().findFirst().get().getDisplayName());
    Assert.assertNull(definitionsList.stream().findFirst().get().getRecordType());
  }

  @Test
  public void testupdateDefinition() {
    // Set up definition ID for getWorkflowType call
    definition.setId(GlobalId.create(REALM_ID, "def-id"));

    Mockito.when(updateDefinitionHandler.process(Mockito.any(), Mockito.any()))
        .thenReturn(definitionInstance);

    Mockito.when(providerHelper.getTemplateDetails(Mockito.any()))
        .thenReturn(Optional.ofNullable(bpmnTemplateDetail));
    Mockito.when(
            definitionServiceHelper.executeAsyncWorkflowTasks(
                Mockito.any(), Mockito.any(), Mockito.anyBoolean()))
        .thenReturn(definition);

    Mockito.when(providerHelper.getTemplateDetails(definition))
        .thenReturn(Optional.ofNullable(bpmnTemplateDetail));
    List<TemplateDetails> dmndetaiList = new ArrayList<>();
    dmndetaiList.add(dmnTemplateDetail);
    Mockito.when(templateService.getReferencedChildTemplates(Mockito.any()))
        .thenReturn(Optional.ofNullable(dmndetaiList));

    Mockito.when(bpmnTemplateDetail.getTemplateName()).thenReturn("invoiceapproval");
    Mockito.doNothing().when(contextHandler).addKey(Mockito.any(WASContextEnums.class), Mockito.anyString());

    Mockito.when(bpmnTemplateDetail.getTemplateData()).thenReturn(BPMN_XML.getBytes());
    Mockito.when(dmnTemplateDetail.getTemplateData()).thenReturn(DMN_XML.getBytes());

    // Mock definitionServiceHelper.getWorkflowType for the new implementation
    Mockito.when(definitionServiceHelper.getWorkflowType(Mockito.anyString(), Mockito.anyString())).thenReturn("approval");

    // RBAC not enabled, should proceed
    Mockito.when(definitionRbacConfig.isEnabledForOperation("UPDATE")).thenReturn(false);
    Definition definitionResp = definitionService.updateDefinition(definition, authorization);
    Assert.assertNotNull(definitionResp);
    Mockito.verify(contextHandler, Mockito.times(1)).addKey(Mockito.any(WASContextEnums.class), Mockito.anyString());
  }

  @Test
  public void testUpdateDefinition_RbacEnabled_AccessDenied() {
    // Set up definition ID for getWorkflowType call (same approach as deleteDefinition)
    definition.setId(GlobalId.create(REALM_ID, "def-id"));

    // Mock definitionServiceHelper.getWorkflowType to return "approval"
    Mockito.when(definitionServiceHelper.getWorkflowType("def-id", REALM_ID)).thenReturn("approval");
    Mockito.when(definitionRbacConfig.isEnabledForOperation("UPDATE")).thenReturn(true);
    Mockito.when(accessVerifier.verifyUserAccess("approval", "UPDATE")).thenReturn(false);
    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS.getErrorMessage());
    definitionService.updateDefinition(definition, authorization);
  }

  @Test
  public void testUpdateDefinition_RbacEnabled_AccessGranted() {
    // Set up definition ID for getWorkflowType call
    definition.setId(GlobalId.create(REALM_ID, "def-id"));
    Authorization testAuthorization = new Authorization();
    testAuthorization.putRealm(REALM_ID);
    testAuthorization.putAuthId("453");

    // Mock template details and validation
    Mockito.when(providerHelper.getTemplateDetails(Mockito.any()))
        .thenReturn(Optional.of(bpmnTemplateDetail));
    Mockito.when(providerHelper.getTemplateDetails(definition))
        .thenReturn(Optional.of(bpmnTemplateDetail));
    List<TemplateDetails> dmndetaiList = new ArrayList<>();
    dmndetaiList.add(dmnTemplateDetail);
    Mockito.when(templateService.getReferencedChildTemplates(Mockito.any()))
        .thenReturn(Optional.of(dmndetaiList));
    Mockito.when(bpmnTemplateDetail.getTemplateData()).thenReturn(BPMN_XML.getBytes());
    Mockito.when(dmnTemplateDetail.getTemplateData()).thenReturn(DMN_XML.getBytes());
    Mockito.when(bpmnTemplateDetail.getTemplateName()).thenReturn("invoiceapproval");

    // Mock all required dependencies for successful update
    Mockito.when(definitionServiceHelper.getWorkflowType("def-id", REALM_ID)).thenReturn("approval");
    Mockito.when(definitionRbacConfig.isEnabledForOperation("UPDATE")).thenReturn(true);
    Mockito.when(accessVerifier.verifyUserAccess("approval", "UPDATE")).thenReturn(true);

    // Mock the update processing
    Mockito.when(updateDefinitionHandler.process(Mockito.any(), Mockito.any()))
        .thenReturn(definitionInstance);
    Mockito.when(definitionServiceHelper.executeAsyncWorkflowTasks(
        Mockito.any(), Mockito.any(), Mockito.anyBoolean()))
        .thenReturn(definition);
    Mockito.doNothing().when(contextHandler).addKey(Mockito.any(WASContextEnums.class), Mockito.anyString());

    Definition result = definitionService.updateDefinition(definition, testAuthorization);
    Assert.assertNotNull(result);
  }



  @SuppressWarnings("unchecked")
  @Test
  public void testGetAllDefinitionsWithWorkflowSteps() {
    RequestContext context = Mockito.mock(RequestContext.class);
    context.setAuthorization(authorization);
    QueryHelper queryHelper = Mockito.mock(QueryHelper.class);
    Mockito.when(providerHelper.checkForWorkflowSteps(Mockito.any())).thenReturn(true);

    DefinitionDetails definitionDetails = buildDefinitionDetails(BPMN_XML_STATEMENTS_ALL_RECURRENCE_RULES.getBytes());

    DefinitionDetails definitionDetailsDmn = new DefinitionDetails();
    definitionDetailsDmn.setDefinitionId("dmnId");
    definitionDetailsDmn.setDefinitionName("invoice");
    definitionDetailsDmn.setModelType(ModelType.DMN);
    definitionDetails.setTemplateDetails(new TemplateDetails());

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();

    definitionDetailsList.add(definitionDetails);
    definitionDetailsList.add(definitionDetailsDmn);

    List<DefinitionDetails> definitionDetailList = new ArrayList<>();
    definitionDetailList.add(definitionDetails);

    ListResult result = definitionService.getAllDefinitions(authorization, queryHelper);
    Assert.assertNotNull(result);
    Assert.assertNull(result.getError());
    Assert.assertNotNull((((List<Definition>) result.getResult())));
    Assert.assertNotNull((((List<Definition>) result.getResult())).stream().findFirst());
  }

  @SuppressWarnings("unchecked")
  @Test
  public void testGetAllDefinitions() {
    RequestContext context = Mockito.mock(RequestContext.class);
    context.setAuthorization(authorization);
    Mockito.when(providerHelper.checkForWorkflowSteps(Mockito.any())).thenReturn(false);
    Optional<List<DefinitionDetails>> definitionLists =
        definitionServiceHelper.getDefinitionList(Long.parseLong(authorization.getRealm()), null);

    DefinitionDetails definitionDetailsDmn = new DefinitionDetails();
    definitionDetailsDmn.setDefinitionId("dmnId");
    definitionDetailsDmn.setDefinitionName("invoice");
    definitionDetailsDmn.setModelType(ModelType.DMN);
    definitionDetailsDmn.setOriginalSetupDate(new Timestamp(new Date().getTime()));
    definitionDetailsDmn.setTemplateDetails(new TemplateDetails());
    definitionDetailsDmn.setStatus(Status.ENABLED);

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();

    definitionDetailsList.add(buildDefinitionDetails(BPMN_XML_STATEMENTS_ALL_RECURRENCE_RULES.getBytes()));
    definitionDetailsList.add(definitionDetailsDmn);

    Mockito.when(
            definitionServiceHelper.getDefinitionListWithoutWorkflowSteps(
                Long.parseLong(authorization.getRealm()), null))
        .thenReturn(Optional.ofNullable(definitionDetailsList));
    ListResult result = definitionService.getAllDefinitions(authorization, null);
    Assert.assertNotNull(result);
    Assert.assertNull(result.getError());
    Assert.assertNotNull(result.getResult());
    Assert.assertNotNull((((List<Definition>) result.getResult())).stream().findFirst());
  }

  @Test
  public void testCreateDefinitionWithDefaultValues_IUSCall() {
    Template template = new Template();
    template.setDisplayName("Test template");
    template.recordType(RecordType.INVOICE.toString());
    template.setCategory("CUSTOM");
    template.addWorkflowSteps(
        new WorkflowStep()
            .action(
                new ActionMapper()
                    .action(
                        new Action()
                            .parameter(
                                new InputParameter()
                                    .fieldValue(WorkflowConstants.CURRENT_USER_PERSONA)))));
    Mockito.when(templateService.getConfigTemplateById("testTemplate", false)).thenReturn(template);
    Mockito.when(identityService.getPersonaId(Mockito.anyString(), Mockito.anyString()))
        .thenReturn("1234567890");
    Definition definition1 = new Definition();
    Mockito.doReturn(definition1)
        .when(definitionService)
        .createDefinition(Mockito.any(), Mockito.any());
    Definition definition =
        definitionService.createDefinitionWithDefaultValues("testTemplate", false, authorization);
    Assert.assertNotNull(definition);
  }

  @Test
  public void testCreateDefinitionWithDefaultValues_WithoutIUSCall() {
    Template template = new Template();
    template.setDisplayName("Test template");
    template.recordType(RecordType.INVOICE.toString());
    template.setCategory("CUSTOM");
    Mockito.when(templateService.getConfigTemplateById("testTemplate", false)).thenReturn(template);
    Definition definition1 = new Definition();
    Mockito.doReturn(definition1)
        .when(definitionService)
        .createDefinition(Mockito.any(), Mockito.any());
    Definition definition =
        definitionService.createDefinitionWithDefaultValues("testTemplate", false, authorization);
    Mockito.verify(identityService, Mockito.never())
        .getPersonaId(Mockito.anyString(), Mockito.anyString());
  }

  @Test
  public void testCreateDefinitionWithDefaultValues_CURRENTUSERGLOBALDataSet() {
    Template template = new Template();
    template.setDisplayName("Test template");
    template.recordType(RecordType.INVOICE.toString());
    template.setCategory("CUSTOM");
    template.addWorkflowSteps(
        new WorkflowStep()
            .action(
                new ActionMapper()
                    .action(
                        new Action()
                            .parameter(
                                new InputParameter()
                                    .fieldValue(WorkflowConstants.CURRENT_USER_GLOBAL)))));
    Mockito.when(templateService.getConfigTemplateById("testTemplate", false)).thenReturn(template);
    Definition definition1 = new Definition();
    Mockito.doReturn(definition1)
        .when(definitionService)
        .createDefinition(Mockito.any(), Mockito.any());
    Definition definition =
        definitionService.createDefinitionWithDefaultValues("testTemplate", false, authorization);
    Assert.assertNotNull(definition);
    Mockito.verify(identityService, Mockito.never())
        .getPersonaId(Mockito.anyString(), Mockito.anyString());
  }

  @Test
  public void testGetDefinitionReadOneStatementDefinition() throws IOException {
    DefinitionDetails definitionDetailsDmn = new DefinitionDetails();
    definitionDetailsDmn.setDefinitionId("dmnId");
    definitionDetailsDmn.setDefinitionName("statement");
    definitionDetailsDmn.setModelType(ModelType.DMN);
    WASContext.setAuthContext(authorization);

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    DefinitionDetails definitionDetails = buildDefinitionDetails(BPMN_XML_STATEMENTS.getBytes(),12345L);
    definitionDetailsList.add(definitionDetails);
    definitionDetailsList.add(definitionDetailsDmn);
    Mockito.when(definitionServiceHelper.findByDefinitionIdOrParentId("defId"))
        .thenReturn(definitionDetailsList);
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("12345");
    Mockito.when(definitionServiceHelper.getMetadata(authorization, definitionDetails))
        .thenCallRealMethod();

    Mockito.when(userDefinitionRead.getBPMNXMLDefinition(any(DefinitionDetails.class)))
        .thenReturn(stmtBpmnResponse);

    Mockito.when(definitionServiceHelper.getDMNXMLDefinition(Mockito.any()))
        .thenReturn(dmnResponseList);

    Template template = new Template();
    Mockito.when(bpmnProcessorImpl.processBpmn(
                    any(DefinitionInstance.class), any(GlobalId.class), anyBoolean()))
            .thenReturn(template);
    Definition definition =
        definitionService.getDefinitionReadOne("defId", TestHelper.getGlobalId("abc"), false);
    Assert.assertNotNull(definition);
    Assert.assertNotNull(definition.getMeta());
    Assert.assertNotNull(definition.getDescription());
    Assert.assertNotNull(definition.getDisplayName());
    Assert.assertNotNull(definition.getRecurrence());
    Assert.assertEquals(2021, definition.getRecurrence().getStartDate().toLocalDate().year().get());
    Assert.assertEquals(RecurTypeEnum.MONTHLY, definition.getRecurrence().getRecurType());
    Assert.assertEquals(2, definition.getRecurrence().getDaysOfMonth().size());
    Assert.assertNull(definition.getRecurrence().getDayOfMonth());
    Assert.assertNull(definition.getRecurrence().getWeekOfMonth());

    Mockito.when(definitionServiceHelper.isActivityDetailsPresent(any())).thenReturn(true);
    Definition multiStepDef =
            definitionService.getDefinitionReadOne("defId", TestHelper.getGlobalId("abc"), true);
    Assert.assertNotNull(multiStepDef);
    Assert.assertNotNull(multiStepDef.getStatus());
    Assert.assertNotNull(multiStepDef.getRecordType());
  }

  @Test
  public void testGetDefinitionReadOneStatementDefinition_testRecurrenceObject() throws IOException {
    Long requestRealm = 12345L;
    DefinitionDetails definitionDetailsDmn = new DefinitionDetails();
    definitionDetailsDmn.setDefinitionId("dmnId");
    definitionDetailsDmn.setDefinitionName("statement");
    definitionDetailsDmn.setOwnerId(12345L);
    definitionDetailsDmn.setModelType(ModelType.DMN);

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    DefinitionDetails definitionDetails = buildDefinitionDetails(
        BPMN_XML_STATEMENTS_ALL_RECURRENCE_RULES.getBytes(),requestRealm);
    definitionDetailsList.add(definitionDetails);
    definitionDetailsList.add(definitionDetailsDmn);
    WASContext.setAuthContext(authorization);
    Mockito.when(definitionServiceHelper.getMetadata(authorization, definitionDetails))
        .thenCallRealMethod();
    Mockito.when(definitionServiceHelper.findByDefinitionIdOrParentId("defId"))
        .thenReturn(definitionDetailsList);
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("12345");

    Mockito.when(userDefinitionRead.getBPMNXMLDefinition(any(DefinitionDetails.class)))
        .thenReturn(stmtBpmnResponse);

    Mockito.when(definitionServiceHelper.getDMNXMLDefinition(any()))
        .thenReturn(dmnResponseList);

    Template template = new Template();
    Mockito.when(bpmnProcessorImpl.processBpmn(
                    any(DefinitionInstance.class), any(GlobalId.class), anyBoolean()))
            .thenReturn(template);
    Definition definition =
        definitionService.getDefinitionReadOne("defId", TestHelper.getGlobalId("abc"), false);
    Assert.assertNotNull(definition);
    Assert.assertNotNull(definition.getMeta());
    Assert.assertNotNull(definition.getDescription());
    Assert.assertNotNull(definition.getDisplayName());
    Assert.assertNotNull(definition.getRecurrence());
    Assert.assertEquals(2021, definition.getRecurrence().getStartDate().toLocalDate().year().get());
    Assert.assertEquals(RecurTypeEnum.MONTHLY, definition.getRecurrence().getRecurType());
    Assert.assertEquals(2, definition.getRecurrence().getDaysOfMonth().size());
    Assert.assertNotNull(definition.getRecurrence().getDaysOfMonth());
    Assert.assertTrue(definition.getRecurrence().getDaysOfMonth().contains(1));
    Assert.assertTrue(definition.getRecurrence().getDaysOfMonth().contains(25));
    Assert.assertNotNull(definition.getRecurrence().getWeekOfMonth());
    Assert.assertEquals(WeekOfMonthEnum.FIRST, definition.getRecurrence().getWeekOfMonth());
    Assert.assertNotNull(definition.getRecurrence().getWeeksOfMonth());
    Assert.assertTrue(definition.getRecurrence().getWeeksOfMonth().contains(WeekOfMonthEnum.FIRST));
    Assert.assertTrue(definition.getRecurrence().getWeeksOfMonth().contains(WeekOfMonthEnum.SECOND));
    Assert.assertNotNull(definition.getRecurrence().getMonthOfYear());
    Assert.assertEquals(MonthsOfYearEnum.JANUARY, definition.getRecurrence().getMonthOfYear());
    Assert.assertNotNull(definition.getRecurrence().getMonthsOfYear());
    Assert.assertTrue(definition.getRecurrence().getMonthsOfYear().contains(MonthsOfYearEnum.JANUARY));
    Assert.assertTrue(definition.getRecurrence().getMonthsOfYear().contains(MonthsOfYearEnum.FEBRUARY));
    Assert.assertFalse(definition.getRecurrence().getMonthsOfYear().contains(MonthsOfYearEnum.MARCH));
    Assert.assertFalse(definition.getRecurrence().getMonthsOfYear().contains(MonthsOfYearEnum.APRIL));
    Assert.assertFalse(definition.getRecurrence().getMonthsOfYear().contains(MonthsOfYearEnum.MAY));
  }

  @Test
  public void testGetDefinitionReadOneUserDefinitionWithoutDefinitionData() throws IOException {
    DefinitionDetails definitionDetailsDmn = new DefinitionDetails();
    definitionDetailsDmn.setDefinitionId("dmnId");
    definitionDetailsDmn.setDefinitionName("invoice");
    definitionDetailsDmn.setModelType(ModelType.DMN);
    WASContext.setAuthContext(authorization);
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    DefinitionDetails definitionDetails = buildDefinitionDetails(
        BPMN_XML_STATEMENTS_ALL_RECURRENCE_RULES.getBytes(),12345L);
    definitionDetailsList.add(definitionDetails);
    definitionDetailsList.add(definitionDetailsDmn);
    Mockito.when(definitionServiceHelper.findByDefinitionIdOrParentId("defId"))
        .thenReturn(definitionDetailsList);
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("12345");

    Mockito.when(userDefinitionRead.getBPMNXMLDefinition(any(DefinitionDetails.class)))
        .thenReturn(bpmnResponse);

    Mockito.when(definitionServiceHelper.getDMNXMLDefinition(Mockito.any()))
        .thenReturn(dmnResponseList);
    Mockito.when(definitionServiceHelper.getMetadata(authorization, definitionDetails))
        .thenCallRealMethod();

    Template template = new Template();
    Mockito.when(bpmnProcessorImpl.processBpmn(
                    any(DefinitionInstance.class), any(GlobalId.class), anyBoolean()))
            .thenReturn(template);
    Definition definition =
        definitionService.getDefinitionReadOne("defId", TestHelper.getGlobalId("abc"), false);
    Assert.assertNotNull(definition);
    Assert.assertNotNull(definition.getMeta());
    Assert.assertNotNull(definition.getDescription());
    Assert.assertNotNull(definition.getDisplayName());
  }

  @Test
  public void testReadOneOrAllDefinition_ConnectorWorkflowId() throws IOException {
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();

    DefinitionDetails definitionDetailsDmn = new DefinitionDetails();
    definitionDetailsDmn.setDefinitionId("dmnId");
    definitionDetailsDmn.setDefinitionName("invoice");
    definitionDetailsDmn.setOwnerId(12345L);
    definitionDetailsDmn.setModelType(ModelType.DMN);

    definitionDetailsList.add(buildDefinitionDetails(BPMN_XML_STATEMENTS_ALL_RECURRENCE_RULES.getBytes(),12345L));
    definitionDetailsList.add(definitionDetailsDmn);
    Mockito.when(definitionServiceHelper.findByDefinitionIdOrParentId("defId"))
            .thenReturn(definitionDetailsList);
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("12345");

    Mockito.when(userDefinitionRead.getBPMNXMLDefinition(any(DefinitionDetails.class)))
            .thenReturn(bpmnResponse);

    Mockito.when(definitionServiceHelper.getDMNXMLDefinition(any()))
            .thenReturn(dmnResponseList);

    Template template = new Template();
    Mockito.when(bpmnProcessorImpl.processBpmn(
                    any(DefinitionInstance.class), any(GlobalId.class), anyBoolean()))
            .thenReturn(template);
    Mockito.when(overwatchConfig.isConnectorWorkflowId()).thenReturn(true);
    Definition definition =
            definitionService.getDefinitionReadOne("defId", TestHelper.getGlobalId("abc"), false);
    Assert.assertNotNull(definition);
    Assert.assertNotNull(definition.getConnectorWorkflowId());
  }

  @Test
  public void testGetAllDefinitionsReturnEmptyList() {
    RequestContext context = Mockito.mock(RequestContext.class);
    context.setAuthorization(authorization);
    Mockito.when(providerHelper.checkForWorkflowSteps(Mockito.any())).thenReturn(false);

    Mockito.when(definitionServiceHelper.getDefinitionListWithoutWorkflowSteps(
                            Long.parseLong(authorization.getRealm()), null))
            .thenReturn(Optional.ofNullable(new ArrayList<>()));
    ListResult result = definitionService.getAllDefinitions(authorization, null);
    Assert.assertNotNull(result);
    Assert.assertNull(result.getError());
    Assert.assertNotNull((((List<Definition>) result.getResult())));
    Assert.assertNotNull((((List<Definition>) result.getResult())).stream().findFirst());
  }

  @Test
  public void testGetAllDefinitions_RbacDisabled() {
    // RBAC disabled for READ operation
    Mockito.when(definitionRbacConfig.isEnabledForOperation("READ")).thenReturn(false);
    Mockito.when(providerHelper.checkForWorkflowSteps(Mockito.any())).thenReturn(false);
    Mockito.when(definitionServiceHelper.getDefinitionListWithoutWorkflowSteps(
                    Long.parseLong(authorization.getRealm()), null))
            .thenReturn(Optional.of(new ArrayList<>()));

    ListResult<Definition> result = definitionService.getAllDefinitions(authorization, null);
    Assert.assertNotNull(result);
  }

  @Test
  public void testGetAllDefinitions_RbacEnabled_AccessGranted() {
    // RBAC enabled and user has access to "default" workflow type
    Mockito.when(definitionRbacConfig.isEnabledForOperation("READ")).thenReturn(true);
    Mockito.when(accessVerifier.verifyUserAccess("default", "READ")).thenReturn(true);
    Mockito.when(providerHelper.checkForWorkflowSteps(Mockito.any())).thenReturn(false);
    Mockito.when(definitionServiceHelper.getDefinitionListWithoutWorkflowSteps(
                    Long.parseLong(authorization.getRealm()), null))
            .thenReturn(Optional.of(new ArrayList<>()));

    ListResult<Definition> result = definitionService.getAllDefinitions(authorization, null);
    Assert.assertNotNull(result);
  }

  @Test
  public void testGetAllDefinitions_RbacEnabled_AccessDenied() {
    // RBAC enabled but user doesn't have access to "default" workflow type
    Mockito.when(definitionRbacConfig.isEnabledForOperation("READ")).thenReturn(true);
    Mockito.when(accessVerifier.verifyUserAccess("default", "READ")).thenReturn(false);

    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS.getErrorMessage());
    definitionService.getAllDefinitions(authorization, null);
  }

  @Test
  public void testGetDefinitionReadOne_RbacDisabled() throws IOException {
    GlobalId id = GlobalId.create(REALM_ID, "def-id");
    WASContext.setAuthContext(authorization);

    // Mock definitionServiceHelper.getWorkflowType to return "approval"
    Mockito.when(definitionServiceHelper.getWorkflowType("def-id", REALM_ID)).thenReturn("approval");
    // RBAC disabled for READ operation
    Mockito.when(definitionRbacConfig.isEnabledForOperation("READ")).thenReturn(false);

    // Mock the dependencies like existing tests
    DefinitionDetails definitionDetails = buildDefinitionDetails(BPMN_XML.getBytes());
    definitionDetails.setOwnerId(Long.valueOf(REALM_ID));
    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    definitionDetailsList.add(definitionDetails);

    Mockito.when(definitionServiceHelper.isActivityDetailsPresent("def-id")).thenReturn(false);
    Mockito.when(definitionServiceHelper.findByDefinitionIdOrParentId("def-id")).thenReturn(definitionDetailsList);
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REALM_ID);
    Mockito.when(definitionServiceHelper.getMetadata(authorization, definitionDetails))
        .thenReturn(new Metadata().createdBy(new User().id(TestHelper.getGlobalId("12"))));

    Mockito.when(singleDefinitionRead.getBPMNXMLDefinition(any(DefinitionDetails.class))).thenReturn(bpmnResponse);

    Template template = new Template();
    template.setName("customApproval");
    Mockito.when(bpmnProcessorImpl.processBpmn(
        any(DefinitionInstance.class), any(GlobalId.class), anyBoolean()))
        .thenReturn(template);

    Definition result = definitionService.getDefinitionReadOne("def-id", id, false);
    Assert.assertNotNull(result);
  }

  @Test
  public void testGetDefinitionReadOne_RbacEnabled_AccessDenied() {
    GlobalId id = GlobalId.create(REALM_ID, "def-id");

    // Mock definitionServiceHelper.getWorkflowType to return "approval"
    Mockito.when(definitionServiceHelper.getWorkflowType("def-id", REALM_ID)).thenReturn("approval");
    Mockito.when(definitionRbacConfig.isEnabledForOperation("READ")).thenReturn(true);
    Mockito.when(accessVerifier.verifyUserAccess("approval", "READ")).thenReturn(false);

    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS.getErrorMessage());
    definitionService.getDefinitionReadOne("def-id", id, false);
  }

  @Test
  public void testGetDefinitionWithObfuscatedValues_RbacEnabled_AccessDenied() {
    final QueryHelper queryHelper = mockQueryHelperWithArgs("def-id", true);
    RequestContext context = Mockito.mock(RequestContext.class);

    // Mock context handler to return realm ID
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REALM_ID);
    // Mock definitionServiceHelper.getWorkflowType to return "approval"
    Mockito.when(definitionServiceHelper.getWorkflowType("def-id", REALM_ID)).thenReturn("approval");
    Mockito.when(definitionRbacConfig.isEnabledForOperation("READ")).thenReturn(true);
    Mockito.when(accessVerifier.verifyUserAccess("approval", "READ")).thenReturn(false);

    exceptionRule.expect(WorkflowGeneralException.class);
    exceptionRule.expectMessage(WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS.getErrorMessage());
    definitionService.getDefinitionWithObfuscatedValues(context, queryHelper, false);
  }

  private DefinitionDetails buildDefinitionDetails(byte[] definitionData) {
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId("defId");
    definitionDetails.setDefinitionName("invoice");
    definitionDetails.setModelType(ModelType.BPMN);
    definitionDetails.setRecordType(RecordType.INVOICE);
    definitionDetails.setStatus(Status.ENABLED);
    definitionDetails.setDefinitionData(definitionData);
    definitionDetails.setTemplateDetails(new TemplateDetails());
    definitionDetails.setDescription("Description");
    definitionDetails.setDefinitionName("displayName");
    definitionDetails.setWorkflowId("1234");
    definitionDetails.setModifiedByUserId(1234567890L);
    definitionDetails.setCreatedByUserId(98765L);
    definitionDetails.setOriginalSetupDate(new Timestamp(new Date().getTime()));
    definitionDetails.setModifiedDate(new Timestamp(new Date().getTime()));
    definitionDetails.setCreatedDate(new Timestamp(new Date().getTime()));

    return definitionDetails;
  }

  private DefinitionDetails buildDefinitionDetails(byte[] definitionData, final Long realmId) {
    DefinitionDetails definitionDetails = new DefinitionDetails();
    definitionDetails.setDefinitionId("defId");
    definitionDetails.setDefinitionName("invoice");
    definitionDetails.setModelType(ModelType.BPMN);
    definitionDetails.setRecordType(RecordType.INVOICE);
    definitionDetails.setStatus(Status.ENABLED);
    definitionDetails.setDefinitionData(definitionData);
    definitionDetails.setTemplateDetails(new TemplateDetails());
    definitionDetails.setDescription("Description");
    definitionDetails.setDefinitionName("displayName");
    definitionDetails.setWorkflowId("1234");
    definitionDetails.setModifiedByUserId(1234567890L);
    definitionDetails.setCreatedByUserId(98765L);
    definitionDetails.setOwnerId(realmId);
    definitionDetails.setModifiedDate(new Timestamp(new Date().getTime()));
    definitionDetails.setCreatedDate(new Timestamp(new Date().getTime()));

    return definitionDetails;
  }

  @Test
  public void testGetDefinitionReadOneForMultiCondition()  {
    DefinitionDetails definitionDetailsDmn = new DefinitionDetails();
    definitionDetailsDmn.setDefinitionId("dmnId");
    definitionDetailsDmn.setDefinitionName("invoice");
    definitionDetailsDmn.setOwnerId(12345L);
    definitionDetailsDmn.setModelType(ModelType.DMN);

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();

    definitionDetailsList.add(buildDefinitionDetails(BPMN_XML_STATEMENTS_ALL_RECURRENCE_RULES.getBytes(),12345L));
    definitionDetailsList.add(definitionDetailsDmn);
    Mockito.when(definitionServiceHelper.findByDefinitionIdOrParentId("defId"))
        .thenReturn(definitionDetailsList);
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("12345");

    Mockito.when(userDefinitionRead.getBPMNXMLDefinition(any(DefinitionDetails.class)))
        .thenReturn(bpmnResponse);

    Mockito.when(definitionServiceHelper.getDMNXMLDefinition(Mockito.any()))
        .thenReturn(dmnResponseList);
    Definition definition =
        definitionService.getDefinitionReadOne("defId", TestHelper.getGlobalId("abc"), true);
    Assert.assertNotNull(definition);
  }

  @Test
  public void testGetDefinitionReadOneForMultiConditionWithObfuscate() {
    final QueryHelper queryHelper = mockQueryHelperWithArgsWithoutObfuscateFlag("defId");
    RequestContext context = Mockito.mock(RequestContext.class);
    DefinitionDetails definitionDetailsDmn = new DefinitionDetails();
    definitionDetailsDmn.setDefinitionId("dmnId");
    definitionDetailsDmn.setDefinitionName("invoice");
    definitionDetailsDmn.setModelType(ModelType.DMN);

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();

    definitionDetailsList.add(
        buildDefinitionDetails(BPMN_XML_STATEMENTS_ALL_RECURRENCE_RULES.getBytes()));
    definitionDetailsList.add(definitionDetailsDmn);

    try {
      definitionService.getDefinitionWithObfuscatedValues(context, queryHelper, false);
    } catch (Exception e) {
      Assert.assertEquals(e.getMessage(), WorkflowError.INVALID_INPUT.getErrorMessage());
    }
  }

  @Test
  public void testGetDefinitionReadOneForMultiConditionWithObfuscateInvalidInput() {
    final QueryHelper queryHelper = mockQueryHelperWithArgsWithoutObfuscateFlag("defId");
    RequestContext context = Mockito.mock(RequestContext.class);
    DefinitionDetails definitionDetailsDmn = new DefinitionDetails();
    definitionDetailsDmn.setDefinitionId("dmnId");
    definitionDetailsDmn.setDefinitionName("invoice");
    definitionDetailsDmn.setModelType(ModelType.DMN);

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();

    definitionDetailsList.add(
            buildDefinitionDetails(BPMN_XML_STATEMENTS_ALL_RECURRENCE_RULES.getBytes()));
    definitionDetailsList.add(definitionDetailsDmn);
    try {
      definitionService.getDefinitionWithObfuscatedValues(context, queryHelper, false);
    } catch (Exception e) {
      Assert.assertEquals(
              e.getMessage(), WorkflowError.INVALID_INPUT.getErrorMessage());
    }
  }

  @Test
  public void testGetDefinitionReadOneForMultiConditionCAAFlagEnabled() {
    DefinitionDetails definitionDetailsDmn = new DefinitionDetails();
    definitionDetailsDmn.setDefinitionId("dmnId");
    definitionDetailsDmn.setDefinitionName("invoice");
    definitionDetailsDmn.setOwnerId(12345L);
    definitionDetailsDmn.setModelType(ModelType.DMN);

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();

    definitionDetailsList.add(
        buildDefinitionDetails(BPMN_XML_STATEMENTS_ALL_RECURRENCE_RULES.getBytes(),12345L));
    definitionDetailsList.add(definitionDetailsDmn);
    Mockito.when(definitionServiceHelper.findByDefinitionIdOrParentId("defId"))
        .thenReturn(definitionDetailsList);
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn(REALM_ID);
    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_USERID)).thenReturn("uId");
    Mockito.doNothing()
        .when(userContributionService)
        .verifyAccess(any(),any());

    Mockito.when(userDefinitionRead.getBPMNXMLDefinition(any(DefinitionDetails.class)))
        .thenReturn(bpmnResponse);

    Mockito.when(definitionServiceHelper.getDMNXMLDefinition(Mockito.any()))
        .thenReturn(dmnResponseList);
    Definition definition =
        definitionService.getDefinitionReadOne("defId", TestHelper.getGlobalId("abc"), true);
    Mockito.verify(userContributionService, Mockito.times(1))
        .verifyAccess(
            Mockito.anyString(), any(UcsVerifyAccessRequest.class));
    Assert.assertNotNull(definition);
  }

  @Test
  public void testGetDefinitionReadOneForMultiConditionCAAFlagEnabledSameRealm() {
    Long requestRealm = 123L;
    Long resourceRealm = 123L;
    DefinitionDetails definitionDetailsDmn = new DefinitionDetails();
    definitionDetailsDmn.setDefinitionId("dmnId");
    definitionDetailsDmn.setDefinitionName("invoice");
    definitionDetailsDmn.setModelType(ModelType.DMN);

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();

    definitionDetailsList.add(
        buildDefinitionDetails(BPMN_XML_STATEMENTS_ALL_RECURRENCE_RULES.getBytes(), resourceRealm));
    definitionDetailsList.add(definitionDetailsDmn);
    Mockito.when(definitionServiceHelper.findByDefinitionIdOrParentId("defId"))
        .thenReturn(definitionDetailsList);

    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID))
        .thenReturn(String.valueOf(requestRealm));
    Mockito.when(contextHandler.get(WASContextEnums.INTUIT_USERID)).thenReturn("uId");

    Mockito.doNothing().when(userContributionService).verifyAccess(any(), any());

    Mockito.when(userDefinitionRead.getBPMNXMLDefinition(any(DefinitionDetails.class)))
        .thenReturn(bpmnResponse);

    Mockito.when(definitionServiceHelper.getDMNXMLDefinition(any()))
        .thenReturn(dmnResponseList);
    Definition definition =
        definitionService.getDefinitionReadOne("defId", TestHelper.getGlobalId("abc"), true);
    Mockito.verify(userContributionService, Mockito.times(1))
        .verifyAccess(Mockito.anyString(), any(UcsVerifyAccessRequest.class));
    Assert.assertNotNull(definition);
  }

  @Test
  public void testGetDefinitionReadOneForNonRecurringReminderWorkflows() throws IOException {
    DefinitionDetails definitionDetailsDmn = new DefinitionDetails();
    definitionDetailsDmn.setDefinitionId("dmnId");
    definitionDetailsDmn.setDefinitionName("invoice");
    definitionDetailsDmn.setOwnerId(12345L);
    definitionDetailsDmn.setModelType(ModelType.DMN);

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();
    DefinitionDetails definitionDetails = buildDefinitionDetails(
        BPMN_XML_STATEMENTS_ALL_RECURRENCE_RULES.getBytes(), 12345L);
    definitionDetailsList.add(definitionDetails);
    definitionDetailsList.add(definitionDetailsDmn);

    Mockito.when(definitionServiceHelper.findByDefinitionIdOrParentId("defId"))
        .thenReturn(definitionDetailsList);
    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("12345");

    Mockito.when(userDefinitionRead.getBPMNXMLDefinition(any(DefinitionDetails.class)))
        .thenReturn(bpmnResponse);

    Mockito.when(definitionServiceHelper.getDMNXMLDefinition(Mockito.any()))
        .thenReturn(dmnResponseList);

    Template template = new Template();
    Mockito.when(bpmnProcessorImpl.processBpmn(
            any(DefinitionInstance.class), any(GlobalId.class), anyBoolean()))
        .thenReturn(template);
    Mockito.when(multiStepReminderTransformer.isMigrationCase(any())).thenReturn(true);

    Definition multiStepDefinition = definitionService.getDefinitionReadOne("defId",
        TestHelper.getGlobalId("abc"), true);
    Mockito.verify(multiStepReminderTransformer, Mockito.times(1)).migrate(any(), any());
  }

  @Test
  public void testGetDefinitionReadOneForMultiConditionWithoutObfuscationCrossRealm() {
    DefinitionDetails definitionDetailsDmn = new DefinitionDetails();
    definitionDetailsDmn.setDefinitionId("dmnId");
    definitionDetailsDmn.setDefinitionName("invoice");
    definitionDetailsDmn.setModelType(ModelType.DMN);

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();

    definitionDetailsList.add(
            buildDefinitionDetails(BPMN_XML_STATEMENTS_ALL_RECURRENCE_RULES.getBytes()));
    definitionDetailsList.add(definitionDetailsDmn);

    Mockito.when(definitionServiceHelper.findByDefinitionIdOrParentId("defId"))
            .thenReturn(definitionDetailsList);

    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("rId");

    try {
      definitionService.getDefinitionReadOne("defId", TestHelper.getGlobalId("abc"), true);
    } catch (Exception e) {
      Assert.assertEquals(
              e.getMessage(), WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS.getErrorMessage());
    }
  }
  @Test
  public void testGetDefinitionReadOneForMultiConditionCAAFlagEnabledException() {
    DefinitionDetails definitionDetailsDmn = new DefinitionDetails();
    definitionDetailsDmn.setDefinitionId("dmnId");
    definitionDetailsDmn.setDefinitionName("invoice");
    definitionDetailsDmn.setModelType(ModelType.DMN);

    List<DefinitionDetails> definitionDetailsList = new ArrayList<>();

    definitionDetailsList.add(
        buildDefinitionDetails(BPMN_XML_STATEMENTS_ALL_RECURRENCE_RULES.getBytes()));
    definitionDetailsList.add(definitionDetailsDmn);

    Mockito.when(definitionServiceHelper.findByDefinitionIdOrParentId("defId"))
        .thenReturn(definitionDetailsList);

    Mockito.when(contextHandler.get(WASContextEnums.OWNER_ID)).thenReturn("rId");
    try {
      definitionService.getDefinitionReadOne("defId", TestHelper.getGlobalId("abc"), true);
    } catch (Exception e) {
      Assert.assertEquals(
          e.getMessage(), WorkflowError.UNAUTHORIZED_RESOURCE_ACCESS.getErrorMessage());
    }
  }

  public static QueryHelper mockQueryHelperWithArgs(
      final String definitionId, final boolean obfuscate) {
    Query query = new Query();
    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    preparedQuery.setType("/workflows/Definition");
    preparedQuery.setName("definitions");
    Map<String, Object> by = new HashMap<>();
    Map<String, String> argsByValue = new HashMap<>();
    argsByValue.put(WorkflowConstants.DEFINITION_ID, definitionId);
    argsByValue.put(OBFUSCATE, String.valueOf(obfuscate));
    by.put(WorkflowConstants.BY, argsByValue);
    preparedQuery.setArgs(by);
    query.setPreparedQuery(preparedQuery);
    QueryHelper queryHelper = new QueryHelper(query);
    return queryHelper;
  }

  public static QueryHelper mockQueryHelperWithArgsWithoutObfuscateFlag(
          final String definitionId) {
    Query query = new Query();
    Query.PreparedQuery preparedQuery = new Query.PreparedQuery();
    preparedQuery.setType("/workflows/Definition");
    preparedQuery.setName("definitions");
    Map<String, Object> by = new HashMap<>();
    Map<String, String> argsByValue = new HashMap<>();
    argsByValue.put(WorkflowConstants.DEFINITION_ID, definitionId);
    by.put(WorkflowConstants.BY, argsByValue);
    preparedQuery.setArgs(by);
    query.setPreparedQuery(preparedQuery);
    QueryHelper queryHelper = new QueryHelper(query);
    return queryHelper;
  }
}
